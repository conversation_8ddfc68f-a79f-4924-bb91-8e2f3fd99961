from collections.abc import Awaitable, Callable
from enum import Enum
from functools import partial
from typing import Any, cast
from uuid import UUID

from langchain_core.runnables import Runnable
from langchain_core.tools import BaseTool, StructuredTool, tool as create_tool
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerative<PERSON><PERSON>
from langfuse import Lang<PERSON>
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.pregel import Pregel
from langgraph.types import interrupt
from langgraph_supervisor import create_supervisor
from pydantic import SecretStr

from app.agentic.context.schemas import ToolDefinition
from app.agentic.context.tools import ToolRegistry
from app.agentic.graph.nodes import (
    context_injector_node,
    fetch_account_node,
    file_search_context_node,
    summarize_account_node,
)
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.agentic.schemas import FrontendToolCallResult
from app.core.config import config
from app.workspace.integrations.user_integrations import UserIntegrations


class GraphKind(str, Enum):
    CONVERSATIONAL = "conversational"
    ASYNCHRONOUS_SUMMARY = "asynchronous_summary"


class GraphFactory:
    def __init__(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
        langfuse_client: Langfuse,
    ):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.agent_prompts = AgentPrompts(langfuse_client)

    async def create(self, kind: GraphKind, **extras) -> StateGraph:
        match kind:
            case GraphKind.CONVERSATIONAL:
                return await self._create_conversational_graph()
            case GraphKind.ASYNCHRONOUS_SUMMARY:
                return await self._create_asynchronous_summary_graph(**extras)
            case _:
                raise ValueError(f"Unknown graph kind {kind}")

    async def _create_conversational_graph(self) -> StateGraph:
        sales_doc_agent = await self._create_sales_document_agent()
        supervisor_agent = await self._create_supervisor_agent([sales_doc_agent])

        graph = StateGraph(ConversationState)

        fetch_account: Callable[[ConversationState], Awaitable[ConversationState]] = (
            partial(fetch_account_node, user_integrations=self.user_integrations)
        )
        context_injector: Callable[
            [ConversationState], Awaitable[ConversationState]
        ] = partial(context_injector_node, agent_prompts=self.agent_prompts)
        file_search_context: Callable[
            [ConversationState], Awaitable[ConversationState]
        ] = partial(file_search_context_node, user_integrations=self.user_integrations)

        graph.add_node("fetch_account", fetch_account)
        graph.add_node("context_injector", context_injector)
        graph.add_node("file_search_context", file_search_context)
        graph.add_node("supervisor", supervisor_agent)

        graph.add_conditional_edges(
            START,
            self._needs_account_refresh,
            {True: "fetch_account", False: "file_search_context"},
        )
        graph.add_edge("fetch_account", "context_injector")
        graph.add_edge("context_injector", "file_search_context")
        graph.add_edge("file_search_context", "supervisor")

        return graph

    async def _create_asynchronous_summary_graph(self) -> StateGraph:
        graph = StateGraph(ConversationState)

        fetch_account: Callable[[ConversationState], Awaitable[ConversationState]] = (
            partial(fetch_account_node, user_integrations=self.user_integrations)
        )
        context_injector: Callable[
            [ConversationState], Awaitable[ConversationState]
        ] = partial(context_injector_node, agent_prompts=self.agent_prompts)
        summarize_account: Callable[
            [ConversationState], Awaitable[ConversationState]
        ] = partial(
            summarize_account_node,
            llm=self._get_llm(),
            agent_prompts=self.agent_prompts,
            user_integrations=self.user_integrations,
        )

        graph.add_node("fetch_account", fetch_account)
        graph.add_node("context_injector", context_injector)
        graph.add_node("summarize_account", summarize_account)

        graph.add_edge(START, "fetch_account")
        graph.add_edge("fetch_account", "context_injector")
        graph.add_edge("context_injector", "summarize_account")

        return graph

    @staticmethod
    def _get_llm() -> ChatGoogleGenerativeAI:
        return ChatGoogleGenerativeAI(
            model="gemini-2.5-flash",
            api_key=SecretStr(config.gemini_api_key),
        )

    async def _create_supervisor_agent(self, agents: list[Pregel]) -> Runnable:
        tools = await self.get_langchain_tools()
        prompt = await self.agent_prompts.get_supervisor_system_prompt()
        return create_supervisor(
            agents=agents,
            model=self._get_llm(),
            tools=tools,
            prompt=prompt,
            state_schema=ConversationState,
            supervisor_name="supervisor",
        ).compile()

    async def _create_sales_document_agent(self) -> Pregel:
        prompt = await self.agent_prompts.get_sales_document_agent_prompt()
        return create_react_agent(
            model=self._get_llm(),
            tools=[],
            prompt=prompt,
            name="sales_document_agent",
            state_schema=ConversationState,
        )

    async def get_langchain_tools(self) -> list[BaseTool | Callable[..., Any]]:
        registry = ToolRegistry(self.user_id, self.user_integrations)
        raw_tools = await registry.get_tools()
        langchain_tools = [self.create_langchain_tool(td) for td in raw_tools]
        return cast("list[BaseTool | Callable[..., Any]]", langchain_tools)

    @staticmethod
    def _needs_account_refresh(state: ConversationState) -> bool:
        return not state.get("last_refetch_at") or not state.get("account_info")

    @staticmethod
    def create_langchain_tool(tool_definition: ToolDefinition) -> BaseTool:
        tool = StructuredTool(
            name=tool_definition.name,
            description=tool_definition.description,
            coroutine=tool_definition.coroutine,
            args_schema=tool_definition.args_schema,
        )

        if not tool_definition.requires_human_review:
            return tool

        @create_tool(
            tool.name, description=tool.description, args_schema=tool.args_schema
        )
        async def call_tool_with_interrupt(**tool_input):
            human_review = interrupt(
                {"tool_name": tool_definition.name, "tool_args": tool_input}
            )
            assert isinstance(human_review, FrontendToolCallResult)
            review_action = human_review.action
            if review_action == "continue":
                tool_response = await tool.ainvoke(tool_input)
            elif review_action == "abort":
                tool_response = "Acknowledge tool call is aborted and do not retry unless explicitly requested."
            else:
                raise ValueError(f"Unsupported action type: {review_action}")

            return tool_response

        return call_tool_with_interrupt
